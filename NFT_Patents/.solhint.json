{"extends": "solhint:recommended", "rules": {"compiler-version": ["error", "^0.8.0"], "func-visibility": ["warn", {"ignoreConstructors": true}], "max-line-length": ["error", 120], "not-rely-on-time": "warn", "avoid-suicide": "error", "avoid-sha3": "warn", "no-inline-assembly": "warn", "no-complex-fallback": "error", "payable-fallback": "error", "no-empty-blocks": "error", "no-unused-vars": "error", "avoid-call-value": "error", "avoid-low-level-calls": "warn", "avoid-tx-origin": "error", "check-send-result": "error", "func-name-mixedcase": "error", "var-name-mixedcase": "error", "const-name-snakecase": "error", "modifier-name-mixedcase": "error", "private-vars-leading-underscore": "error", "use-forbidden-name": "error", "imports-on-top": "error", "ordering": "warn", "no-global-import": "error", "visibility-modifier-order": "error", "bracket-align": "error", "code-complexity": ["error", 8], "function-max-lines": ["error", 50], "max-states-count": ["error", 15], "no-console": "warn", "reason-string": ["warn", {"maxLength": 64}]}}