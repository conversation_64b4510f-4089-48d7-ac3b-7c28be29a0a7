# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=patent_nft_db
DB_USER=postgres
DB_PASSWORD=your_password_here

# Blockchain Configuration
ETHEREUM_RPC_URL=http://localhost:8545
ETHEREUM_NETWORK=localhost
PRIVATE_KEY=your_private_key_here

# Contract Addresses (will be populated after deployment)
PSP_TOKEN_ADDRESS=
SEARCH_PAYMENT_ADDRESS=
PATENT_NFT_ADDRESS=

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
USPTO_API_KEY=your_uspto_api_key_here

# Security
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
