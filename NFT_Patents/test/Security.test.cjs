const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Security Tests", function () {
  let patentNFT, pspToken, searchPayment;
  let owner, attacker, user1, user2;

  beforeEach(async function () {
    [owner, attacker, user1, user2] = await ethers.getSigners();

    // Deploy contracts
    const PatentNFT = await ethers.getContractFactory("PatentNFT");
    patentNFT = await PatentNFT.deploy();
    await patentNFT.waitForDeployment();

    const PSPToken = await ethers.getContractFactory("PSPToken");
    const initialTokenPrice = ethers.parseEther("0.00001");
    pspToken = await PSPToken.deploy(initialTokenPrice);
    await pspToken.waitForDeployment();

    const SearchPayment = await ethers.getContractFactory("SearchPayment");
    const ethPrice = ethers.parseEther("0.002");
    const usdcPrice = ethers.parseUnits("5", 6);
    const pspPrice = ethers.parseEther("500");
    
    // Mock USDC address for testing
    const mockUSDC = await ethers.getContractFactory("PSPToken");
    const mockUSDCToken = await mockUSDC.deploy(ethers.parseEther("0.001"));
    await mockUSDCToken.waitForDeployment();

    searchPayment = await SearchPayment.deploy(
      await pspToken.getAddress(),
      await mockUSDCToken.getAddress(),
      ethPrice,
      usdcPrice,
      pspPrice
    );
    await searchPayment.waitForDeployment();

    // Setup authorization
    await pspToken.setAuthorizedSpender(await searchPayment.getAddress(), true);
  });

  describe("Access Control Tests", function () {
    it("Should prevent non-owner from verifying patents", async function () {
      // Mint a patent first
      await patentNFT.mintPatent(
        user1.address,
        "https://ipfs.io/ipfs/test",
        "Test Patent",
        "Test Inventor",
        "US-********-B2"
      );

      // Try to verify as non-owner
      await expect(
        patentNFT.connect(attacker).verifyPatent(1)
      ).to.be.revertedWithCustomError(patentNFT, "OwnableUnauthorizedAccount");
    });

    it("Should prevent non-owner from updating PSP token price", async function () {
      const newPrice = ethers.parseEther("0.00002");
      
      await expect(
        pspToken.connect(attacker).updateTokenPrice(newPrice)
      ).to.be.revertedWithCustomError(pspToken, "OwnableUnauthorizedAccount");
    });

    it("Should prevent non-owner from pausing contracts", async function () {
      await expect(
        pspToken.connect(attacker).pause()
      ).to.be.revertedWithCustomError(pspToken, "OwnableUnauthorizedAccount");

      await expect(
        searchPayment.connect(attacker).pause()
      ).to.be.revertedWithCustomError(searchPayment, "OwnableUnauthorizedAccount");
    });

    it("Should prevent unauthorized spending of PSP tokens", async function () {
      // User purchases tokens
      const ethAmount = ethers.parseEther("0.01");
      await pspToken.connect(user1).purchaseTokens({ value: ethAmount });

      // Attacker tries to spend user's tokens
      const userBalance = await pspToken.balanceOf(user1.address);
      
      await expect(
        pspToken.connect(attacker).spendTokensFor(user1.address, userBalance)
      ).to.be.revertedWith("Not authorized to spend tokens");
    });
  });

  describe("Reentrancy Protection Tests", function () {
    it("Should prevent reentrancy in PSP token purchase", async function () {
      // This test would require a malicious contract to test properly
      // For now, we verify the nonReentrant modifier is present
      const ethAmount = ethers.parseEther("0.01");
      
      // Multiple rapid calls should not cause issues
      await pspToken.connect(user1).purchaseTokens({ value: ethAmount });
      await pspToken.connect(user1).purchaseTokens({ value: ethAmount });
      
      expect(await pspToken.balanceOf(user1.address)).to.be.gt(0);
    });

    it("Should prevent reentrancy in search payment", async function () {
      // Setup user with PSP tokens
      const ethAmount = ethers.parseEther("0.01");
      await pspToken.connect(user1).purchaseTokens({ value: ethAmount });
      
      // Approve spending
      const pspAmount = ethers.parseEther("500");
      await pspToken.connect(user1).approve(await searchPayment.getAddress(), pspAmount);
      
      // Payment should work normally
      await searchPayment.connect(user1).payForSearch();
      
      expect(await searchPayment.userSearchesPurchased(user1.address)).to.equal(1);
    });
  });

  describe("Input Validation Tests", function () {
    it("Should reject invalid patent number formats", async function () {
      await expect(
        patentNFT.mintPatent(
          user1.address,
          "https://ipfs.io/ipfs/test",
          "Test Patent",
          "Test Inventor",
          "" // Empty patent number
        )
      ).to.be.revertedWith("Patent number required");
    });

    it("Should reject zero address in patent minting", async function () {
      await expect(
        patentNFT.mintPatent(
          ethers.ZeroAddress,
          "https://ipfs.io/ipfs/test",
          "Test Patent",
          "Test Inventor",
          "US-********-B2"
        )
      ).to.be.revertedWith("Invalid recipient address");
    });

    it("Should reject empty strings in patent data", async function () {
      await expect(
        patentNFT.mintPatent(
          user1.address,
          "", // Empty URI
          "Test Patent",
          "Test Inventor",
          "US-********-B2"
        )
      ).to.be.revertedWith("Token URI required");

      await expect(
        patentNFT.mintPatent(
          user1.address,
          "https://ipfs.io/ipfs/test",
          "", // Empty title
          "Test Inventor",
          "US-********-B2"
        )
      ).to.be.revertedWith("Title required");
    });

    it("Should reject zero price updates", async function () {
      await expect(
        pspToken.updateTokenPrice(0)
      ).to.be.revertedWith("Price must be greater than 0");
    });
  });

  describe("Economic Security Tests", function () {
    it("Should respect PSP token max supply", async function () {
      const maxSupply = await pspToken.MAX_SUPPLY();
      const currentSupply = await pspToken.totalSupply();
      const remainingSupply = maxSupply - currentSupply;
      
      // Try to mint more than max supply
      await expect(
        pspToken.mint(owner.address, remainingSupply + ethers.parseEther("1"))
      ).to.be.revertedWith("Would exceed maximum supply");
    });

    it("Should handle insufficient balance scenarios", async function () {
      // Try to redeem more tokens than user has
      await expect(
        pspToken.connect(user1).redeemTokens(ethers.parseEther("1000"))
      ).to.be.revertedWith("Insufficient token balance");
    });

    it("Should prevent payment without sufficient PSP balance", async function () {
      // User has no PSP tokens
      await expect(
        searchPayment.connect(user1).payForSearch()
      ).to.be.revertedWith("Insufficient PSP token balance");
    });
  });

  describe("Pause Functionality Tests", function () {
    it("Should prevent operations when PSP token is paused", async function () {
      await pspToken.pause();
      
      await expect(
        pspToken.connect(user1).purchaseTokens({ value: ethers.parseEther("0.01") })
      ).to.be.revertedWithCustomError(pspToken, "EnforcedPause");
      
      await expect(
        pspToken.connect(user1).redeemTokens(ethers.parseEther("100"))
      ).to.be.revertedWithCustomError(pspToken, "EnforcedPause");
    });

    it("Should prevent payments when SearchPayment is paused", async function () {
      // Setup user with tokens first
      const ethAmount = ethers.parseEther("0.01");
      await pspToken.connect(user1).purchaseTokens({ value: ethAmount });
      await pspToken.connect(user1).approve(await searchPayment.getAddress(), ethers.parseEther("500"));
      
      // Pause the contract
      await searchPayment.pause();
      
      await expect(
        searchPayment.connect(user1).payForSearch()
      ).to.be.revertedWithCustomError(searchPayment, "EnforcedPause");
    });
  });
});
